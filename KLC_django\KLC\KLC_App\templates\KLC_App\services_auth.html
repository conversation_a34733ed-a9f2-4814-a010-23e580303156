{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تأكيد الهوية | الخدمات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body {
            min-height: 100vh;
            background: url('{% static "images/services-background.jpg" %}') no-repeat center center fixed;
            background-size: cover;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .auth-card {
            background: rgba(255,255,255,0.95);
            border-radius: 18px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.18);
            padding: 2.5rem 2rem 2rem 2rem;
            max-width: 400px;
            width: 100%;
        }
        .logo {
            display: block;
            margin: 0 auto 1.5rem auto;
            height: 70px;
        }
        .form-label {
            font-weight: bold;
        }
        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13,110,253,.25);
        }
        .auth-title {
            text-align: center;
            margin-bottom: 1.2rem;
            font-weight: bold;
            color: #0d6efd;
        }
    </style>
</head>
<body>
    <div class="auth-card">
        <img src="{% static 'images/logo.png' %}" alt="Logo" class="logo">
        <h2 class="auth-title">تأكيد الهوية للدخول إلى الخدمات</h2>
        <form method="post" autocomplete="off">
            {% csrf_token %}
            <div class="mb-3">
                <label for="full_name" class="form-label">الاسم الرباعي</label>
                <input type="text" class="form-control" id="full_name" name="full_name" required placeholder="مثال: محمد أحمد علي يوسف">
            </div>
            <div class="mb-3">
                <label for="national_id" class="form-label">رقم الهوية</label>
                <input type="text" class="form-control" id="national_id" name="national_id" required placeholder="مثال: 123456789">
            </div>
            {% if error %}
            <div class="alert alert-danger text-center">{{ error }}</div>
            {% endif %}
            <button type="submit" class="btn btn-primary w-100">تأكيد</button>
        </form>
    </div>
</body>
</html>
